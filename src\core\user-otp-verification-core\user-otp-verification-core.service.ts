import { Injectable } from '@nestjs/common';
import { OtpVerification, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { UserOTPVerificationCorePaginateDto } from './dto/user-otp-verification-core.dto';
import { userSessionMessage } from 'src/shared/keys/user.key';

@Injectable()
export class OTPVerificationCoreService extends PrismaBaseRepository<
  OtpVerification,
  UserOTPVerificationCorePaginateDto,
  Prisma.OtpVerificationCreateArgs,
  Prisma.OtpVerificationUpdateArgs,
  Prisma.OtpVerificationUpdateManyArgs,
  Prisma.OtpVerificationFindUniqueArgs,
  Prisma.OtpVerificationFindFirstArgs,
  Prisma.OtpVerificationFindManyArgs,
  Prisma.OtpVerificationDeleteArgs,
  Prisma.OtpVerificationDeleteManyArgs,
  Prisma.OtpVerificationCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.otpVerification, {
      NOT_FOUND: userSessionMessage.USER_SESSION_NOT_FOUND,
      DELETED: userSessionMessage.USER_SESSION_IS_DELETED,
    });
  }
}
