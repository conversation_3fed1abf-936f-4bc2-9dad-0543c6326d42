import { Injectable } from '@nestjs/common';
import { UserSession, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { UserSessionCorePaginateDto } from './dto/user-session-core.dto';
import { userSessionMessage } from 'src/shared/keys/user.key';

@Injectable()
export class UserSessionCoreService extends PrismaBaseRepository<
  UserSession,
  UserSessionCorePaginateDto,
  Prisma.UserSessionCreateArgs,
  Prisma.UserSessionUpdateArgs,
  Prisma.UserSessionUpdateManyArgs,
  Prisma.UserSessionFindUniqueArgs,
  Prisma.UserSessionFindFirstArgs,
  Prisma.UserSessionFindManyArgs,
  Prisma.UserSessionDeleteArgs,
  Prisma.UserSessionDeleteManyArgs,
  Prisma.UserSessionCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.userSession, {
      NOT_FOUND: userSessionMessage.USER_SESSION_NOT_FOUND,
      DELETED: userSessionMessage.USER_SESSION_IS_DELETED,
    });
  }
}
