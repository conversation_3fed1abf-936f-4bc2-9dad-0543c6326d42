import {
  createParamDecorator,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { UserSessionType } from '../types/user-session.type';

import admin from 'firebase-admin';
import { authMessages } from '../keys/user.key';
import { PrismaClient, STATUS } from '@prisma/client';

export const GetUserSession = createParamDecorator(
  async (data: any, ctx: ExecutionContext): Promise<UserSessionType> => {
    if (data) {
      console.log('data', data);
    }
    const request = ctx.switchToHttp().getRequest();
    const headers = request.headers;

    if (!headers.authorization) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_NOT_FOUND);
    }

    const authHeaderValue = headers.authorization;

    if (!authHeaderValue.startsWith('Bearer')) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_IS_NOT_BEARER);
    }

    const parts = authHeaderValue.split(' ');

    if (parts.length !== 2) {
      throw new UnauthorizedException(authMessages.INVALID_AUTH_HEADER_BEARER);
    }

    const token = parts[1];

    const prisma = new PrismaClient();

    try {
      const decodedToken = await admin.auth().verifyIdToken(token);

      const user = await prisma.user.findUniqueOrThrow({
        where: {
          firebaseUid: decodedToken.uid,
          isDeleted: false,
        },
      });

      if (!user) {
        throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
      }

      if (user.status === STATUS.DISABLED || user.isDeleted) {
        throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
      }

      return {
        user,
      };
    } catch (error) {
      console.error('Error: ', error.message);
      throw new UnauthorizedException(authMessages.TOKEN_EXPIRED);
    } finally {
      await prisma.$disconnect();
    }
  },
);

export const GetUserRequestHeader = createParamDecorator(
  (data: any, ctx: ExecutionContext): UserSessionType => {
    if (data) {
      console.log('data', data);
    }
    const request = ctx.switchToHttp().getRequest();
    return request.headers;
  },
);

// export const GetCustomUserSession = createParamDecorator(
//   async (data: any, ctx: ExecutionContext): Promise<UserSessionType> => {
//     if (data) {
//       console.log('data', data);
//     }
//     const request = ctx.switchToHttp().getRequest();
//     const headers = request.headers;

//     // if authorization header is there then use it otherwise make it public
//     if (headers.authorization) {
//       const authHeaderValue = headers.authorization;

//       if (!authHeaderValue.startsWith('Bearer')) {
//         throw new UnauthorizedException(authMessages.AUTH_HEADER_IS_NOT_BEARER);
//       }

//       const parts = authHeaderValue.split(' ');

//       if (parts.length !== 2) {
//         throw new UnauthorizedException(
//           authMessages.INVALID_AUTH_HEADER_BEARER,
//         );
//       }

//       const token = parts[1];

//       const prisma = new PrismaClient();
//       try {
//         const decodedToken = await admin.auth().verifyIdToken(token);

//         const user = await prisma.user.findUniqueOrThrow({
//           where: {
//             firebaseUId: decodedToken.uid,
//             isDeleted: false,
//           },
//         });

//         if (!user) {
//           throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
//         }

//         if (user.status === Status.DISABLED || user.isDeleted) {
//           throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
//         }

//         const findUserPlatformWiseSession = await prisma.userSession.findFirst({
//           where: {
//             isDeleted: false,
//             userId: user.id,
//           },
//           orderBy: {
//             updatedAt: 'desc',
//           },
//         });

//         if (findUserPlatformWiseSession) {
//           const update = await prisma.userSession.update({
//             where: {
//               id: findUserPlatformWiseSession.id,
//             },
//             data: {
//               userPlatform:
//                 request.headers.userplatform === 'APP'
//                   ? UserPlatform.APP
//                   : UserPlatform.WEB,
//             },
//           });
//         }

//         return {
//           user,
//         };
//       } catch (error) {
//         console.error('Error: ', error.message);
//         throw new UnauthorizedException(authMessages.TOKEN_EXPIRED);
//       } finally {
//         await prisma.$disconnect();
//       }
//     } else {
//       const childId = headers.childid;

//       if (childId) {
//         const prisma = new PrismaClient();
//         try {
//           const user_platform = request.headers['userplatform'];

//           const childSession = await prisma.userSession.findFirst({
//             where: {
//               isDeleted: false,
//               childId: childId,
//               userType: UserType.CHILD,
//             },
//             orderBy: {
//               updatedAt: 'desc',
//             },
//           });

//           if (childSession) {
//             await prisma.userSession.update({
//               where: {
//                 id: childSession.id,
//               },
//               data: {
//                 userPlatform:
//                   user_platform === 'APP' ? UserPlatform.APP : UserPlatform.WEB,
//               },
//             });
//           }
//         } catch (error) {
//           console.error('Error: ', error.message);
//         } finally {
//           await prisma.$disconnect();
//         }
//         return;
//       }

//       return;
//     }
//   },
// );
