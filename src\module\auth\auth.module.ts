import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { UserCoreModule } from 'src/core/user-core/user-core.module';
import { UserSessionCoreModule } from 'src/core/user-session-core/user-session-core.module';
import { OTPVerificationCoreModule } from 'src/core/user-otp-verification-core/user-otp-verification-core.module';
import { AuthService } from './auth.service';
import { PrismaService } from '../../shared/module/prisma/prisma.service';
import { EmailService } from '../../shared/services/email.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [UserCoreModule, UserSessionCoreModule, OTPVerificationCoreModule],
  controllers: [AuthController],
  providers: [AuthService, EmailService],
  exports: [AuthService],
})
export class AuthModule {}
